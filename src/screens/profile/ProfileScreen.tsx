import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import React, {useEffect, useMemo, useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useIsFocused} from '@react-navigation/native';
import {profileActions, screenFocusActions} from 'store';
import {Header, WrapperContainer} from 'components';
import {fonts, images} from 'assets';
import {colors} from 'assets';
import {scale} from 'utils/device';
import {
  PersonalDetail,
  IDAndAddress,
  ManageWallets,
  ManageBanks,
  PasswordSettings,
  Security,
  LegalDocuments,
} from './components';
import {selectProfileInfo} from 'store/reducers/profile/selector';

interface Props {}

interface TabItemProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
}

interface VerifiedHeaderProps {
  isVerified: boolean;
}

const TabItem: React.FC<TabItemProps> = React.memo(
  ({label, isActive, onPress}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.tabItem}
      onPress={onPress}>
      <Text style={styles.tabText}>{label}</Text>
      {isActive && <View style={styles.activeDot} />}
    </TouchableOpacity>
  ),
);

const VerifiedHeader: React.FC<VerifiedHeaderProps> = React.memo(
  ({isVerified}) => (
    <View style={styles.headerContainer}>
      <Text style={styles.title}>Profile</Text>
      {isVerified && (
        <View style={styles.verifiedContainer}>
          <Text style={styles.subtitle}> Successfully Verified</Text>
          <Image source={images.ic_verified} style={styles.verifiedIcon} />
        </View>
      )}
    </View>
  ),
);

interface VerificationNoticeProps {
  isVerified: boolean;
  isEditMode: boolean;
  onModeChange: (isEdit: boolean) => void;
  onPress: () => void;
}

const VerificationNotice: React.FC<VerificationNoticeProps> = React.memo(
  ({isVerified, isEditMode, onModeChange, onPress}) => {
    if (!isVerified) return null;

    return (
      <TouchableOpacity style={styles.verificationNotice} onPress={onPress}>
        <View style={styles.noticeContent}>
          <Image source={images.ic_user_check} style={styles.noticeIcon} />
          <Text style={styles.noticeText}>
            Your Information Have Been{'\n'}Successfully Verified!!
          </Text>
        </View>
        <View style={styles.modeButtons}>
          <TouchableOpacity
            style={[styles.modeButton, !isEditMode && styles.activeModeButton]}
            onPress={() => onModeChange(false)}>
            <Text
              style={[
                styles.modeButtonText,
                !isEditMode && styles.activeModeButtonText,
              ]}>
              Preview
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.modeButton, isEditMode && styles.activeModeButton]}
            onPress={() => onModeChange(true)}>
            <Text
              style={[
                styles.modeButtonText,
                isEditMode && styles.activeModeButtonText,
              ]}>
              Edit
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  },
);

const ProfileScreen: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [activeTab, setActiveTab] = React.useState(0);
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [showPersonalDetail, setShowPersonalDetail] = React.useState(false);
  const profileInfo = useSelector(selectProfileInfo);

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('ProfileScreen'));
      dispatch(profileActions.getProfile());
    }
  }, [isFocused, dispatch]);

  const profileItems = useMemo(
    () => [
      'Personal Detail',
      'ID & Address',
      'Manage Wallets',
      'Manage Banks',
      'Password Settings',
      'Security',
      'Legal Documents',
    ],
    [],
  );

  const handleTabPress = useCallback(
    (index: number) => () => setActiveTab(index),
    [],
  );

  const handleModeChange = useCallback((isEdit: boolean) => {
    setIsEditMode(isEdit);
  }, []);

  const renderTabHeader = useMemo(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabContainer}>
        {profileItems.map((item, index) => (
          <TabItem
            key={item}
            label={item}
            isActive={index === activeTab}
            onPress={handleTabPress(index)}
          />
        ))}
      </ScrollView>
    );
  }, [activeTab, profileItems, handleTabPress]);

  const renderContent = useMemo(() => {
    switch (activeTab) {
      case 0:
        return showPersonalDetail ? (
          <PersonalDetail isEditMode={isEditMode} />
        ) : null;
      case 1:
        return <IDAndAddress />;
      case 2:
        return <ManageWallets />;
      case 3:
        return <ManageBanks />;
      case 4:
        return <PasswordSettings />;
      case 5:
        return <Security />;
      case 6:
        return <LegalDocuments />;
      default:
        return null;
    }
  }, [activeTab, isEditMode, showPersonalDetail]);

  const isVerified = useMemo(() => {
    return profileInfo.data?.is_verify === 1;
  }, [profileInfo.data]);

  const handleVerificationNoticePress = useCallback(() => {
    setShowPersonalDetail(true);
  }, []);

  return (
    <WrapperContainer style={{backgroundColor: colors.black2}}>
      <Header />
      <View style={styles.container}>
        <VerifiedHeader isVerified={isVerified} />
        {renderTabHeader}
        <VerificationNotice
          isVerified={isVerified}
          isEditMode={isEditMode}
          onModeChange={handleModeChange}
          onPress={handleVerificationNoticePress}
        />
      </View>
      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>{renderContent}</View>
      </ScrollView>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.background,
  },
  container: {
    paddingHorizontal: scale(16),
    paddingTop: scale(34),
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: scale(40),
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  rightSection: {
    alignItems: 'flex-end',
    gap: scale(8),
  },
  verifiedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(2),
  },
  subtitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.yellow,
  },
  verifiedIcon: {
    width: scale(24),
    height: scale(24),
  },
  modeButtons: {
    flexDirection: 'row',
    gap: scale(8),
  },
  modeButton: {
    paddingHorizontal: scale(12),
    paddingVertical: scale(6),
    borderRadius: scale(5),
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  activeModeButton: {
    backgroundColor: colors.yellow,
    borderColor: colors.yellow,
  },
  modeButtonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  activeModeButtonText: {
    color: colors.black,
  },
  verificationNotice: {
    backgroundColor: colors.black3,
    borderRadius: scale(12),
    padding: scale(16),
    marginTop: scale(20),
    marginBottom: scale(20),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noticeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  noticeIcon: {
    width: scale(40),
    height: scale(40),
    marginRight: scale(12),
  },
  noticeText: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    flex: 1,
  },
  tabContainer: {
    gap: scale(16),
    paddingBottom: scale(20),
  },
  tabItem: {
    position: 'relative',
    alignItems: 'center',
  },
  activeDot: {
    width: scale(5),
    height: scale(5),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    position: 'absolute',
    bottom: scale(-8),
  },
  tabText: {
    color: colors.white,
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
  },
  contentContainer: {},
  content: {
    gap: scale(24),
  },
  inputContainer: {
    gap: scale(8),
  },
  inputSection: {
    paddingTop: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(16),
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default React.memo(ProfileScreen);
